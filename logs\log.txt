========================================
         3D AI Studio Launcher
========================================

[1/5] Checking Python installation...
Γ£ô Python found
[2/4] Checking virtual environment...
Γ£ô Virtual environment ready
[3/4] Quick environment check...
  [INFO] Full dependency and model management now handled in-app
  [INFO] Use Dependency Manager for detailed status and repairs
Quick environment check...
  [INFO] Full validation available through Dependency Manager
  ✓ Main Application: Basic setup OK
  ✓ Trellis: Basic setup OK
  ✓ Hunyuan3D-2: Basic setup OK
  Γ£ô Basic environments ready
[4/4] Activating Python environment...
Installing missing startup dependencies...
Warning: Some startup dependencies may not have installed correctly
The application will attempt to start anyway
  Γ£ô Python environment activated
Checking Node.js for Electron app...
Γ£ô Node.js found
Preparing desktop application...
Γ£ô Desktop application ready
Starting 3D AI Studio Desktop App...

========================================
    3D AI Studio Desktop Application
    Starting in desktop window...

    ≡ƒÄ» NEW: DEPENDENCY MANAGER
    All models and environments now managed in-app!

    FIRST TIME SETUP:
    1. Click the Dependency Manager button (database icon)
    2. Use "Environments" tab to repair any dependency issues
    3. Use "Models" tab to download essential AI models
    4. All models stored in organized ./models/ folders
    5. Run 'python migrate_models.py' to organize existing models

    Γ£à No more manual dependency management needed!
========================================


> 3d-ai-studio@1.0.0 electron
> electron .


Starting 3D AI Studio...
Starting new server...
Starting server...
App path: E:\3D AI Studio
Python path: E:\3D AI Studio\.venv\Scripts\python.exe
App script: E:\3D AI Studio\app.py
Server: 19:34:07 | INFO | [SYSTEM](NO_SESSI) | System startup | Data: {"platform": "Windows-11-10.0.26100-SP0", "python_version": "3.12.10", "cpu_count": 88, "memory_total": "79.9 GB", "disk_free": "530.6 GB", "torch_version": "2.6.0+cu124", "cuda_available": true, "cuda_device": "NVIDIA GeForce RTX 3060", "cuda_memory": "12.0 GB"}
19:34:07 | INFO | [STARTUP](NO_SESSI) | 3D AI Studio starting up

Server: Starting 3D AI Studio core application...
All AI models and dependencies managed through Dependency Manager
Core application ready - use Dependency Manager for AI features
*** MODEL_MANAGER_API.PY MODULE BEING IMPORTED - VERSION 2.0 WITH UNICODE FIXES ***
*** MODEL MANAGER API ROUTES BEING REGISTERED ***
*** THIS PROVES THE FILE IS BEING LOADED ***
*** DEPENDENCY_MANAGER_API.PY MODULE BEING IMPORTED ***
*** UNIFIED DEPENDENCY SYSTEM LOADED ***
*** DEPENDENCY MANAGER API ROUTES BEING REGISTERED ***
*** REGISTERING UNIFIED DEPENDENCY SYSTEM ***
Starting 3D AI Studio server...
Running startup dependency checks...
============================================================
STARTUP DEPENDENCY CHECKS
============================================================
[STARTUP] Checking critical dependencies...

Server: [STARTUP] imageio: Already available

Server: [STARTUP] Installing Pillow...

Server: [STARTUP] Pillow: Installed successfully

Server: [STARTUP] numpy: Already available

Server: [STARTUP] requests: Already available

Server: [STARTUP] flask: Already available

Server: [STARTUP] flask-cors: Already available
[STARTUP] Critical dependencies check complete: 6/6 available

Server: [STARTUP] PIL imports working correctly

Server: [STARTUP] ImageIO already available
============================================================
STARTUP CHECKS COMPLETE: 3/3 successful
============================================================

Server: 19:34:17 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Background services using main app logging system

Server: Initializing background services...
19:34:17 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Initializing background services...
19:34:17 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Registered service: hunyuan3d_native
19:34:17 | INFO | [SYSTEM](NO_SESSI) | Registered Hunyuan3D-2 native server service

Server: 19:34:17 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Background Services Status Report:
19:34:17 | INFO | [SYSTEM](NO_SESSI) | Started service monitoring

Server: 19:34:17 | INFO | [BACKGROUND_SERVICES](NO_SESSI) |   STOPPED hunyuan3d_native: stopped (restarts: 0/1)
19:34:17 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Background Services Status Report:
19:34:17 | INFO | [BACKGROUND_SERVICES](NO_SESSI) |   STOPPED hunyuan3d_native: stopped (restarts: 0/1)

Server: 19:34:17 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Background services initialized successfully
Starting background services...
19:34:17 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Starting background services...
[BACKGROUND] Starting background services...
[BACKGROUND] Starting hunyuan3d_native...
[BACKGROUND] Starting hunyuan3d_native...
19:34:17 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Starting service: hunyuan3d_native

Server: [BACKGROUND] Executing batch file: run-with-text_to_3d.bat
19:34:18 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Started batch service hunyuan3d_native with PID 32876 (hidden window)

Server: [BACKGROUND] Waiting for hunyuan3d_native to be ready...
19:34:20 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Service hunyuan3d_native started successfully and is ready

Server: [BACKGROUND] hunyuan3d_native started successfully
19:34:22 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | Background Services Status Report:
19:34:22 | INFO | [BACKGROUND_SERVICES](NO_SESSI) |   RUNNING hunyuan3d_native: running (restarts: 0/1)

Server: 19:34:22 | INFO | [BACKGROUND_SERVICES](NO_SESSI) |     Health check: http://localhost:8080/
19:34:22 | INFO | [BACKGROUND_SERVICES](NO_SESSI) | All background services started successfully

Server: [BACKGROUND] Background services started successfully
Background services initialization completed
 * Serving Flask app 'app'
 * Debug mode: off

Server stderr: WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
Press CTRL+C to quit

Server detected as ready via stderr!
Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:25] "GET / HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:25] "GET /assets/index-pnrTHhta.js HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:25] "GET /assets/index-ehFM-iJR.css HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:26] "GET /api/config/huggingface-token HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:26] "GET /api/sample-images HTTP/1.1" 200 -

Server: 19:34:26 | INFO | [API](NO_SESSI) | API: GET delighter_status | Status: 200 | Duration: 0.028s | IP: 127.0.0.1

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:26] "GET /api/delighter/status HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:27] "GET /api/sample-images/∩┐╜Pngtree∩┐╜3d%20rendering%20of%20a%20greek_15549830-sharpen.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:27] "GET /api/sample-images/a793b9fc-3485-4c04-b57d-95cdb356c57c-upscale-3.4x.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:27] "GET /api/sample-images/5ac3a645-10a0-48c7-93e4-c53a5a1c81a7-upscale-3.4x.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:27] "GET /api/sample-images/b0da10f6-3d28-4a74-95b8-3fa373c2d7a2.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:27] "GET /api/sample-images/d9ba77aa-ff91-4e43-ad55-fabe51244dc8.png HTTP/1.1
Server stderr: " 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:27] "GET /api/sample-images/d5aeb72c-82af-45f1-b518-ea72280119b3-sharpen-face-upscale-3.4x.jpeg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:27] "
Server stderr: GET /api/sample-images/02f234e7-140d-4775-9563-067b4e5862c4.png HTTP/1.1
Server stderr: " 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:27] "
Server stderr: GET /api/sample-images/10745cfe-80a0-48f0-a56e-0308e9f870f2-upscale-3.4x.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:27] "GET /api/sample-images/18722263-upscale-4x.jpeg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:27] "GET /api/sample-images/50abdc8c-5c16-4bda-9ffa-e5ba71adb3eb-sharpen-upscale-3.4x.jpeg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:28] "GET /api/sample-images/da6939f2-25df-4133-94af-8b242e042314.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:28] "GET /api/sample-images/DP-23915-004-sharpen.jpeg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:28] "GET /api/sample-images/ef257036-61ce-40d0-b627-83761afc45ed-upscale-3.4x.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:28] "GET /api/sample-images/fede52c1-c968-4daa-88e7-c753af5887ad.png HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:28] "GET /api/sample-images/istockphoto-173559916-612x612.jpg HTTP/1.1" 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:28] "GET /api/sample-images/mayan-pot-in-the-form-of-a-kneeling-figure-800-1200_u-l-q1nhita0.jpg HTTP/1.1
Server stderr: " 304 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:34:38] "GET /api/pipelines/available HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:35:16] "GET /api/sample-images/5ac3a645-10a0-48c7-93e4-c53a5a1c81a7-upscale-3.4x.png HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:35:17] "POST /api/proxy-image HTTP/1.1" 200 -

Server: [SPARSE] Backend: spconv, Attention: flash_attn
Warning: Could not import Trellis modules: No module named 'open3d'
Trying to use Trellis environment at: E:\3D AI Studio\Resources\TRELLIS\app\env\Scripts\python.exe
Trellis pipeline loaded successfully
Hunyuan3D-2 will use dedicated environment - skipping main environment imports
Using HuggingFace Hunyuan3D-2 models
Hunyuan3D-2 pipeline loaded successfully
Hunyuan3D-2 dedicated environment is available
19:35:17 | INFO | [UPLOAD](9c55fdda) | Image upload started: sample-image.jpg | Data: {"filename": "sample-image.jpg", "image_id": "9c55fdda-ef2e-4bbb-8c7f-240c1821076d"}

Server: 19:35:17 | INFO | [BACKGROUND_REMOVAL](9c55fdda) | Starting background removal for sample-image.jpg

Server: REMBG loaded successfully
19:35:17 | INFO | [BACKGROUND_REMOVAL](9c55fdda) | Image opened successfully. Format: PNG, Size: (3461, 3461)
19:35:17 | INFO | [BACKGROUND_REMOVAL](9c55fdda) | Removing background...

Server: 19:35:21 | INFO | [BACKGROUND_REMOVAL](9c55fdda) | Background removal completed in 3.83s

Server: Saving processed image...
Processed image saved to E:\3D AI Studio\uploads\9c55fdda-ef2e-4bbb-8c7f-240c1821076d\processed_sample-image.png
Converting to base64 for preview...
Base64 conversion completed
Background removal completed successfully for sample-image.jpg
19:35:24 | INFO | [API](9c55fdda) | API: POST upload_image | Status: 200 | Duration: 7.457s | IP: 127.0.0.1

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:35:24] "POST /api/upload HTTP/1.1" 200 -

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:35:45] "GET /api/progress/9c55fdda-ef2e-4bbb-8c7f-240c1821076d HTTP/1.1" 200 -

Server: DEBUG: Received settings from frontend: {'ss_steps': 12, 'ss_cfg_strength': 7.5, 'slat_steps': 12, 'slat_cfg_strength': 3, 'randomize_seed': True, 'seed': 464600, 'simplify': 0.95, 'texture_size': 1024, 'enable_lighting_optimizer': True, 'hunyuan_model': 'turbo', 'octree_resolution': 128, 'num_inference_steps': 5, 'guidance_scale': 5, 'enable_texture': True, 'face_count': 40000, 'enable_delighter': False, 'delighter_quality': 'high'}
DEBUG: Pipeline type: hunyuan3d
DEBUG: enable_lighting_optimizer in settings: True
DEBUG: enable_lighting_optimizer value: True
DEBUG: Selecting pipeline for type: hunyuan3d
DEBUG: Attempting to load Hunyuan3D-2 pipeline...
DEBUG: Hunyuan3D-2 pipeline object created successfully
Hunyuan3D-2 dedicated environment is available
DEBUG: Hunyuan3D-2 availability check result: True
SUCCESS: Selected Hunyuan3D-2 pipeline for image-to-3D generation
DEBUG: Hunyuan3D-2 model path: tencent/Hunyuan3D-2mini
DEBUG: Hunyuan3D-2 subfolder: hunyuan3d-dit-v2-mini-turbo
Created project: Image Model 9c55fdda (ID: bcca1274-7828-40ed-a006-a12649fbab78)
Updated project bcca1274-7828-40ed-a006-a12649fbab78 settings
HUNYUAN3D: Starting Hunyuan3D-2 model generation with settings: {'seed': 464600, 'hunyuan_model': 'turbo', 'octree_resolution': 128, 'num_inference_steps': 5, 'guidance_scale': 5, 'enable_texture': True, 'enable_texture_enhancement': False, 'face_count': 40000}
HUNYUAN3D: Input image path: E:\3D AI Studio\uploads\9c55fdda-ef2e-4bbb-8c7f-240c1821076d\processed_sample-image.png
HUNYUAN3D: Pipeline type: Hunyuan3DPipeline
HUNYUAN3D: Pipeline model path: tencent/Hunyuan3D-2mini
HUNYUAN3D: Calling selected_pipeline.process_image() for Hunyuan3D-2...
PROGRESS DEBUG: Using session_id for progress tracking: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
Progress update - Session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d, Stage: generation, Progress: 0%, Description: Starting native Hunyuan3D-2 generation...
SSE connection established for session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress update - Session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d, Stage: generation, Progress: 10%, Description: Connecting to native Hunyuan3D-2 server...
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress update - Session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d, Stage: generation, Progress: 100%, Description: Native generation failed: Failed to connect to Gradio server: 'charmap' codec can't encode character '\u2714' in position 38: character maps to <undefined>
Native Hunyuan3D-2 service error: Native Hunyuan3D-2 service failed: Failed to connect to Gradio server: 'charmap' codec can't encode character '\u2714' in position 38: character maps to <undefined>
Progress update - Session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d, Stage: generation, Progress: 100%, Description: Native generation failed: Native Hunyuan3D-2 service failed: Failed to connect to Gradio server: 'charmap' codec can't encode character '\u2714' in position 38: character maps to <undefined>
Native service failed: Native Hunyuan3D-2 service error: Native Hunyuan3D-2 service failed: Failed to connect to Gradio server: 'charmap' codec can't encode character '\u2714' in position 38: character maps to <undefined>
PROGRESS DEBUG: Starting Hunyuan3D-2 generation with session_id: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
Progress update - Session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d, Stage: generation, Progress: 0%, Description: Starting Hunyuan3D-2 generation...
PROGRESS DEBUG: Initial progress update sent for session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
HUNYUAN3D: Starting subprocess with 1800s timeout...
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Server:
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
Server stderr: 127.0.0.1 - - [05/Jun/2025 19:36:49] "GET /api/progress/9c55fdda-ef2e-4bbb-8c7f-240c1821076d HTTP/1.1" 200 -

Server:
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
SSE connection closed for session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
SSE connection established for session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Server:
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
Server stderr: 127.0.0.1 - - [05/Jun/2025 19:37:53] "GET /api/progress/9c55fdda-ef2e-4bbb-8c7f-240c1821076d HTTP/1.1" 200 -

Server:
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
SSE connection closed for session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
SSE connection established for session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Server:
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
Server stderr: 127.0.0.1 - - [05/Jun/2025 19:38:56] "GET /api/progress/9c55fdda-ef2e-4bbb-8c7f-240c1821076d HTTP/1.1" 200 -

Server:
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
SSE connection closed for session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
SSE connection established for session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Server:
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
Server:
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
19:39:49 | INFO | [BACKGROUND_SERVICES](9c55fdda) | Background Services Status Report:
19:39:49 | INFO | [BACKGROUND_SERVICES](9c55fdda) |   RUNNING hunyuan3d_native: running (restarts: 0/1)

Server: 19:39:49 | INFO | [BACKGROUND_SERVICES](9c55fdda) |     Health check: http://localhost:8080/

Server stderr: 127.0.0.1 - - [05/Jun/2025 19:40:00] "GET /api/progress/9c55fdda-ef2e-4bbb-8c7f-240c1821076d HTTP/1.1" 200 -

Server: Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
SSE connection closed for session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
SSE connection established for session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
Server:
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Server stderr: 127.0.0.1 - - [05/Jun/2025 19:41:03] "GET /api/progress/9c55fdda-ef2e-4bbb-8c7f-240c1821076d HTTP/1.1" 200 -

Server:
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
SSE connection closed for session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
SSE connection established for session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
Server:
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Server stderr: 127.0.0.1 - - [05/Jun/2025 19:42:07] "GET /api/progress/9c55fdda-ef2e-4bbb-8c7f-240c1821076d HTTP/1.1" 200 -

Server:
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
SSE connection closed for session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
SSE connection established for session: 9c55fdda-ef2e-4bbb-8c7f-240c1821076d
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages
SSE DEBUG: Sending 1 stages to frontend: ['generation']
Progress retrieved for session 9c55fdda-ef2e-4bbb-8c7f-240c1821076d: 1 stages

<--- Last few GCs --->

[33728:00000DAC0019C000]   219121 ms: Scavenge 5.0 (6.4) -> 4.6 (6.4) MB, pooled: 3 MB, 3.02 / 0.05 ms  (average mu = 1.000, current mu = 1.000) task;
[33728:00000DAC0019C000]   374993 ms: Scavenge 5.4 (6.6) -> 4.8 (6.9) MB, pooled: 2 MB, 1.38 / 0.07 ms  (average mu = 1.000, current mu = 1.000) task;
[33728:00000DAC0019C000]   521082 ms: Scavenge 5.6 (6.9) -> 5.0 (7.9) MB, pooled: 2 MB, 1050.59 / 0.08 ms  (average mu = 1.000, current mu = 1.000) task;

[33728:0605/194242.929:ERROR:electron\shell\common\node_bindings.cc:178] OOM error in V8: ExternalEntityTable::AllocateEntry Allocation failed - process out of memory