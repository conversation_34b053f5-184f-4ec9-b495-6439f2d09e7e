#!/usr/bin/env python3
"""
Native Hunyuan3D-2 Service Integration
Uses the fully compiled and working Hunyuan3D-2 WinPortable environment
"""

import os
import time
import base64
import json
import subprocess
import requests
from pathlib import Path
from typing import Dict, Any, Optional, Callable
from PIL import Image
import tempfile
import threading
import psutil

class Hunyuan3DNativeService:
    """Service that manages the native Hunyuan3D-2 server and processes requests."""
    
    def __init__(self):
        self.winportable_path = Path("E:/3D AI Studio/Resources/Hunyuan3D2_WinPortable")
        self.server_process = None
        self.server_port = 7860  # Default Gradio port
        self.api_port = 8080     # API server port (from log)
        self.is_running = False
        self.startup_timeout = 300  # 5 minutes for server startup
        
    def _find_available_port(self, start_port: int = 7860) -> int:
        """Find an available port starting from the given port."""
        for port in range(start_port, start_port + 100):
            try:
                import socket
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                continue
        raise RuntimeError("No available ports found")
    
    def start_server(self, progress_callback: Optional[Callable] = None) -> bool:
        """Start the native Hunyuan3D-2 server."""
        try:
            if self.is_server_running():
                if progress_callback:
                    progress_callback("Native Hunyuan3D-2 server already running")
                return True
            
            if progress_callback:
                progress_callback("Starting native Hunyuan3D-2 server...")
            
            # Find available ports
            self.server_port = self._find_available_port(7860)
            self.api_port = self._find_available_port(8081)
            
            # Start the API server (better for integration)
            api_script = self.winportable_path / "Hunyuan3D-2" / "api_server.py"
            python_exe = self.winportable_path / "python_standalone" / "python.exe"
            
            if not api_script.exists():
                raise FileNotFoundError(f"API server script not found: {api_script}")
            
            if not python_exe.exists():
                raise FileNotFoundError(f"Python executable not found: {python_exe}")
            
            # Set up environment
            env = os.environ.copy()
            env["PATH"] = f"{self.winportable_path / 'MinGit' / 'cmd'};{self.winportable_path / 'python_standalone' / 'Scripts'};{env.get('PATH', '')}"
            env["PYTHONPYCACHEPREFIX"] = str(self.winportable_path / "pycache")
            env["HF_HUB_CACHE"] = str(self.winportable_path / "HuggingFaceHub")
            env["HY3DGEN_MODELS"] = str(self.winportable_path / "HuggingFaceHub")
            
            if progress_callback:
                progress_callback("Launching Hunyuan3D-2 API server...")
            
            # Start the server process
            self.server_process = subprocess.Popen([
                str(python_exe),
                str(api_script),
                "--host", "127.0.0.1",
                "--port", str(self.api_port),
                "--enable_tex"  # Enable texture generation
            ], 
            cwd=str(self.winportable_path / "Hunyuan3D-2"),
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
            )
            
            # Wait for server to start
            if progress_callback:
                progress_callback("Waiting for server to initialize...")
            
            start_time = time.time()
            while time.time() - start_time < self.startup_timeout:
                if self.is_server_running():
                    self.is_running = True
                    if progress_callback:
                        progress_callback(f"Native Hunyuan3D-2 server started on port {self.api_port}")
                    return True
                
                # Check if process died
                if self.server_process.poll() is not None:
                    stdout, stderr = self.server_process.communicate()
                    raise RuntimeError(f"Server process died: {stderr}")
                
                time.sleep(2)
            
            raise TimeoutError(f"Server failed to start within {self.startup_timeout} seconds")
            
        except Exception as e:
            if progress_callback:
                progress_callback(f"Failed to start server: {str(e)}")
            return False
    
    def stop_server(self) -> bool:
        """Stop the native Hunyuan3D-2 server."""
        try:
            if self.server_process:
                # Terminate gracefully
                self.server_process.terminate()
                try:
                    self.server_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    # Force kill if needed
                    self.server_process.kill()
                    self.server_process.wait()
                
                self.server_process = None
            
            # Kill any remaining processes
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'api_server.py' in ' '.join(proc.info['cmdline'] or []):
                        proc.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            self.is_running = False
            return True
            
        except Exception as e:
            print(f"Error stopping server: {e}")
            return False
    
    def is_server_running(self) -> bool:
        """Check if the server is running and responding."""
        try:
            # Try the root endpoint first
            response = requests.get(f"http://localhost:{self.api_port}/", timeout=5)
            return response.status_code in [200, 404, 422]  # 422 is expected for GET on POST endpoint
        except:
            try:
                # Fallback to docs endpoint
                response = requests.get(f"http://localhost:{self.api_port}/docs", timeout=5)
                return response.status_code == 200
            except:
                return False
    
    def generate_3d_model(self,
                         image: Image.Image,
                         settings: Dict[str, Any],
                         progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """Generate a 3D model using the native Hunyuan3D-2 Gradio server."""
        try:
            if progress_callback:
                progress_callback("Connecting to native Hunyuan3D-2 server...")

            # Import Gradio client
            try:
                from gradio_client import Client, handle_file
            except ImportError:
                return {
                    "success": False,
                    "error": "Gradio client not available. Install with: pip install gradio_client"
                }

            # Connect to the Gradio server
            try:
                client = Client(f"http://localhost:{self.api_port}")
                if progress_callback:
                    progress_callback("Connected to native Hunyuan3D-2 server")
            except Exception as e:
                # Ensure error message is ASCII-safe
                error_msg = str(e).encode('ascii', 'ignore').decode('ascii')
                return {
                    "success": False,
                    "error": f"Failed to connect to Gradio server: {error_msg}"
                }

            if progress_callback:
                progress_callback("Preparing image for native processing...")

            # Save image to temporary file (Gradio client needs file paths)
            import tempfile
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
                image.save(tmp_file.name, format='PNG')
                temp_image_path = tmp_file.name

            try:
                # Determine which endpoint to use based on texture setting
                enable_texture = settings.get("enable_texture", True)
                api_endpoint = "/generation_all" if enable_texture else "/shape_generation"

                if progress_callback:
                    if enable_texture:
                        progress_callback("Starting full generation with texture (this may take several minutes)...")
                    else:
                        progress_callback("Starting shape generation...")

                # Call the Gradio API
                result = client.predict(
                    None,                                           # caption (text prompt)
                    handle_file(temp_image_path),                   # image file
                    None,                                           # mv_image_front
                    None,                                           # mv_image_back
                    None,                                           # mv_image_left
                    None,                                           # mv_image_right
                    settings.get("num_inference_steps", 5),        # steps
                    settings.get("guidance_scale", 5.0),           # guidance_scale
                    settings.get("seed", 1234),                    # seed
                    settings.get("octree_resolution", 128),        # octree_resolution
                    True,                                           # check_box_rembg (always remove background)
                    settings.get("num_chunks", 8000),              # num_chunks
                    False,                                          # randomize_seed
                    api_name=api_endpoint
                )

                if progress_callback:
                    progress_callback("Processing generation results...")

                # Extract the generated file path
                output_file = None
                if result and len(result) > 0:
                    if enable_texture and len(result) >= 2:
                        # For textured generation, get the textured mesh (second result)
                        textured_result = result[1]
                        if isinstance(textured_result, dict) and 'value' in textured_result:
                            output_file = textured_result['value']
                    else:
                        # For shape-only generation, get the first result
                        shape_result = result[0]
                        if isinstance(shape_result, dict) and 'value' in shape_result:
                            output_file = shape_result['value']

                if output_file and Path(output_file).exists():
                    # Copy to our output directory with a proper name
                    output_dir = Path("output")
                    output_dir.mkdir(exist_ok=True)

                    timestamp = int(time.time() * 1000000)
                    final_output_path = output_dir / f"hunyuan3d_native_{timestamp}.glb"

                    import shutil
                    shutil.copy2(output_file, final_output_path)

                    if progress_callback:
                        progress_callback("Native Hunyuan3D-2 generation completed successfully!")

                    return {
                        "success": True,
                        "output_path": str(final_output_path),
                        "message": f"Native Hunyuan3D-2 generation completed {'with full texture pipeline' if enable_texture else 'shape only'}"
                    }
                else:
                    return {
                        "success": False,
                        "error": "No valid output file received from native server"
                    }

            finally:
                # Clean up temporary file
                try:
                    Path(temp_image_path).unlink(missing_ok=True)
                except:
                    pass

        except Exception as e:
            # Ensure error message is ASCII-safe
            error_msg = str(e).encode('ascii', 'ignore').decode('ascii')
            return {
                "success": False,
                "error": f"Native service error: {error_msg}"
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Get the current status of the native service."""
        return {
            "available": self.winportable_path.exists(),
            "running": self.is_server_running(),
            "server_port": self.api_port,
            "winportable_path": str(self.winportable_path)
        }


# Global service instance
_native_service = None

def get_native_service() -> Hunyuan3DNativeService:
    """Get the global native service instance."""
    global _native_service
    if _native_service is None:
        _native_service = Hunyuan3DNativeService()
    return _native_service


if __name__ == "__main__":
    # Test the service
    service = get_native_service()
    print(f"Service status: {service.get_status()}")
    
    if service.winportable_path.exists():
        print("SUCCESS: Native Hunyuan3D-2 environment found!")

        # Test server startup
        print("Testing server startup...")
        if service.start_server(lambda msg: print(f"  {msg}")):
            print("SUCCESS: Server started successfully!")

            # Test server stop
            print("Testing server stop...")
            if service.stop_server():
                print("SUCCESS: Server stopped successfully!")
            else:
                print("ERROR: Failed to stop server")
        else:
            print("ERROR: Failed to start server")
    else:
        print("ERROR: Native Hunyuan3D-2 environment not found")
